# Firebase Cloud Function /notify Feature Tracker

## Project Overview
**Goal**: Complete the Firebase Cloud Function push-notification feature for train location-based notifications.

**Current Status**: Client-side infrastructure complete, backend Cloud Functions missing.

---

## 1 · Already Implemented ✅

### Client-Side Components
- [x] **FcmTokenService** - `lib/services/fcm_token_service.dart`
  - Complete FCM token management with server sync
  
- [x] **FirebaseMessagingService** - `lib/services/firebase_messaging_service.dart`
  - Handles foreground/background FCM messages
  
- [x] **OnboardingNotificationService** - `lib/services/notification_services/onboarding_notification_service.dart`
  - Comprehensive notification processing logic
  
- [x] **NotificationTestingScreen** - `lib/screens/notification_testing/notification_testing_screen.dart`
  - Manual testing interface with real coordinates
  
- [x] **OnboardingResponse Model** - `lib/types/attendance_types/onboarding_response.dart`
  - Model for /microservice/train/location/ API response
  
- [x] **LocationService** - `lib/services/attendance_services/location_service.dart`
  - Fetches train location data from microservice endpoint
  
- [x] **NotificationPreferencesModel** - `lib/models/notification_preferences_model.dart`
  - User notification preferences with sound/vibration settings

### Platform Configuration
- [x] **Android notification channel** - `android/app/src/main/AndroidManifest.xml`
  - "high_importance_channel" configured
  
- [x] **Android notification icon** - `android/app/src/main/res/drawable/ic_notification.xml`
  - Custom notification icon
  
- [x] **Android notification color** - `android/app/src/main/res/values/colors.xml`
  - Green notification color (#4CAF50)
  
- [x] **iOS Firebase setup** - `ios/Runner/AppDelegate.swift`
  - Firebase configured with notification permissions
  
- [x] **Firebase project config** - Android/iOS google-services files
  - railwaysapp-prod project configured

---

## 2 · TODO (Missing Implementation) ❌

### 1. Firebase Cloud Functions Infrastructure
- [ ] **Task**: Create Firebase Functions directory structure and POST /notify endpoint
- **File**: `functions/src/index.ts`
- **Priority**: HIGH
- **Code Stub**:
```typescript
import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

export const notify = functions.https.onRequest(async (req, res) => {
  // Fetch /microservice/train/location/, build table, send FCM
});
```


### 2. Firebase Functions Package Configuration
- [ ] **Task**: Initialize Firebase Functions with required dependencies
- **File**: `functions/package.json`
- **Priority**: HIGH
- **Code Stub**:
```json
{
  "dependencies": {
    "firebase-admin": "^12.0.0",
    "firebase-functions": "^4.0.0"
  }
}
```

### 3. RAILOPS_BEARER Secret Configuration
- [ ] **Task**: Store API bearer token as Firebase Functions secret
- **Command**: `firebase functions:secrets:set RAILOPS_BEARER`
- **Priority**: HIGH

### 4. Firestore Token Storage Schema
- [ ] **Task**: Implement Firestore collection structure for FCM tokens
- **File**: `functions/src/index.ts` (token lookup logic)
- **Priority**: HIGH
- **Code Stub**:
```typescript
const getUserToken = async (userId: string) => {
  const doc = await admin.firestore().collection('tokens').doc(userId).get();
  return doc.data()?.fcm_token;
};
```

### 5. Firestore Anti-Duplication Schema
- [ ] **Task**: Implement sentAlerts collection to prevent duplicate notifications
- **File**: `functions/src/index.ts` (anti-dup logic)
- **Priority**: MEDIUM
- **Code Stub**:
```typescript
const alertKey = `${userId}/${date}/${station}`;
await admin.firestore().collection('sentAlerts').doc(alertKey).set({timestamp: Date.now()});
```

### 6. Custom Notification Sound Assets
- [ ] **Task**: Add railops_alarm sound files for Android and iOS
- **Files**: 
  - `android/app/src/main/res/raw/railops_alarm.mp3`
  - `ios/Runner/railops_alarm.caf`
- **Priority**: MEDIUM

### 7. RailOps Alerts Notification Channel
- [ ] **Task**: Create dedicated notification channel for train alerts
- **File**: `lib/services/firebase_messaging_service.dart` (modify setupFlutterNotifications)
- **Priority**: MEDIUM
- **Code Stub**:
```dart
await _flutterLocalNotificationsPlugin
  .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
  ?.createNotificationChannel(const AndroidNotificationChannel(
    'railops_alerts', 'RailOps Train Alerts',
    description: 'Critical train boarding/off-boarding notifications',
    importance: Importance.max,
    sound: RawResourceAndroidNotificationSound('railops_alarm'),
  ));
```

### 8. Snooze Functionality Implementation
- [ ] **Task**: Add 10-minute snooze action handling in Flutter notification system
- **File**: `lib/services/firebase_messaging_service.dart`
- **Priority**: LOW
- **Code Stub**:
```dart
const AndroidNotificationDetails(
  actions: [
    AndroidNotificationAction('snooze_10min', 'Snooze 10 min'),
    AndroidNotificationAction('dismiss', 'Dismiss'),
  ],
);
```

### 9. Firebase Functions Deployment Configuration
- [ ] **Task**: Create Firebase configuration for Functions deployment
- **File**: `firebase.json`
- **Priority**: HIGH
- **Code Stub**:
```json
{
  "functions": {
    "source": "functions",
    "runtime": "nodejs18"
  }
}
```

### 10. API Documentation and Testing
- [ ] **Task**: Create README with curl examples for backend integration
- **File**: `functions/README.md`
- **Priority**: LOW
- **Code Stub**:
```bash
curl -X POST https://your-region-railwaysapp-prod.cloudfunctions.net/notify \
  -H "Content-Type: application/json" \
  -d '{"user_id": "123", "lat": "28.6139", "lng": "77.2090"}'
```

---

## Implementation Priority Order

### Phase 1: Core Backend (HIGH Priority)
1. Firebase Functions Infrastructure (#1)
2. Package Configuration (#2)
3. Secret Configuration (#3)
4. Token Storage Schema (#4)
5. Deployment Configuration (#9)

### Phase 2: Notification Enhancement (MEDIUM Priority)
6. Anti-Duplication Schema (#5)
7. Custom Sound Assets (#6)
8. RailOps Alerts Channel (#7)

### Phase 3: Polish & Documentation (LOW Priority)
9. Snooze Functionality (#8)
10. API Documentation (#10)

---

## Notes
- Firebase project: `railwaysapp-prod` (ID: 513557807469)
- Package name: `com.biputri.railops`
- Train location API: `/microservice/train/location/`
- 50km radius filtering handled by backend
- Coach table format: StationCode | Coach | Onboarding/Off-boarding/Vacant counts

---

**Last Updated**: December 2024
**Status**: Ready for Phase 1 implementation
